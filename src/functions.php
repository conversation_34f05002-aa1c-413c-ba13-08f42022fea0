<?php
/**
 * scd functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package scd
 */

use Drew<PERSON>\MailChimp\MailChimp;

if ( ! defined( 'SCD_VERSION' ) ) {
	define( 'SCD_VERSION', '1.0.0' );
}

// Define short date format
define( 'SCD_DATE', 'j. n. Y' );

// Load modules
require get_template_directory() . '/inc/template-tags.php';
require get_template_directory() . '/inc/template-functions.php';
require get_template_directory() . '/inc/class-scd-config.php';
require get_template_directory() . '/inc/class-relevance-sorter.php';
include get_template_directory() . '/inc/importer.php';
include get_template_directory() . '/inc/cli-helpers.php';

// Disable unnecessary WordPress features
remove_action( 'wp_head', 'wp_generator' );
remove_action( 'wp_head', 'rsd_link' );
remove_action( 'wp_head', 'wlwmanifest_link' );
add_filter( 'feed_links_show_comments_feed', '__return_false' );
add_filter( 'xmlrpc_enabled', '__return_false' );
add_filter( 'big_image_size_threshold', '__return_false' );

// Do not redirect untranslated content
add_filter( 'pll_check_canonical_url', '__return_false' );

// Email settings
add_filter( 'wp_mail_from_name', '__return_empty_string' );
add_filter( 'wp_mail_from', 'scd_email_from', 100 );
function scd_email_from( $from_email ) {
	return '<EMAIL>';
}

// Disable customizer support script
add_action( 'wp_before_admin_bar_render', 'scd_disable_customize_support_script', 0 );
function scd_disable_customize_support_script() {
	remove_action( 'wp_before_admin_bar_render', 'wp_customize_support_script' );
}

// Disable jQuery Migrate
add_action( 'wp_default_scripts', 'scd_remove_jquery_migrate' );
function scd_remove_jquery_migrate( $scripts ) {
	if ( ! is_admin() && isset( $scripts->registered['jquery'] ) ) {
		$script = $scripts->registered['jquery'];

		if ( $script->deps ) {
			$script->deps = array_diff( $script->deps, array( 'jquery-migrate' ) );
		}
	}
}

// Theme setup
add_action( 'after_setup_theme', 'scd_setup' );
function scd_setup() {
	/*
	 * Make theme available for translation.
	 * Translations can be filed in the /languages/ directory.
	 */
	load_theme_textdomain( 'scd', get_template_directory() . '/languages' );

	// Add default posts and comments RSS feed links to head.
	add_theme_support( 'automatic-feed-links' );

	/*
	 * Let WordPress manage the document title.
	 * By adding theme support, we declare that this theme does not use a
	 * hard-coded <title> tag in the document head, and expect WordPress to
	 * provide it for us.
	 */
	add_theme_support( 'title-tag' );

	/*
	 * Enable support for Post Thumbnails on posts and pages.
	 *
	 * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
	 */
	add_theme_support( 'post-thumbnails' );

	// Register navigation menus
	register_nav_menus(
		array(
			'header-top' => esc_html__( 'Top navigation', 'scd' ),
			'header-main' => esc_html__( 'Main navigation', 'scd' ),
			'footer-quick-links' => esc_html__( 'Quick links', 'scd' ),
		)
	);

	/*
	 * Switch default core markup for search form, comment form, and comments
	 * to output valid HTML5.
	 */
	add_theme_support(
		'html5',
		array(
			'search-form',
			'comment-form',
			'comment-list',
			'gallery',
			'caption',
			'style',
			'script',
		)
	);

	// Add theme support for selective refresh for widgets.
	add_theme_support( 'customize-selective-refresh-widgets' );

	/**
	 * Register our sidebars and widgetized areas.
	 *
	 */
	function arphabet_widgets_init() {

		register_sidebar( array(
			'name'          => 'Home right sidebar',
			'id'            => 'home_right_1',
			'before_widget' => '<div>',
			'after_widget'  => '</div>',
			'before_title'  => '<h2 class="rounded">',
			'after_title'   => '</h2>',
		) );

	}
	add_action( 'widgets_init', 'arphabet_widgets_init' );	

	/**
	 * Add support for core custom logo.
	 *
	 * @link https://codex.wordpress.org/Theme_Logo
	 */
	add_theme_support(
		'custom-logo',
		array(
			'height'      => 250,
			'width'       => 250,
			'flex-width'  => true,
			'flex-height' => true,
		)
	);

	// Disable admin bar push down CSS
	add_theme_support( 'admin-bar', array( 'callback' => '__return_false' ) );
}

// Use home.php template for tag and category pages, except of "latest news" category
add_filter( 'tag_template', 'scd_home_template_include' );
add_filter( 'category_template', 'scd_home_template_include' );
function scd_home_template_include( $template ) {
	global $SCD_CPT;
	if ( is_category( $SCD_CPT->latest_news_cat_id() ) ) {
		return $template;
	}
	return ! empty( $home_template = locate_template( array( 'home.php' ) ) ) ? $home_template : $template;
}

// Handle single template includes
add_filter( 'single_template', 'scd_single_template_include' );
function scd_single_template_include( $template ) {

	$template_include = '';

	if ( is_singular( array( 'scd_dielo_smd', 'scd_dielo_ncd', 'scd_hra' ) ) ) {
		$template_include = locate_template( array( 'single-dielo.php' ) );
	} elseif ( is_singular( array( 'scd_publikacia', 'scd_pub_kniznice' ) ) ) {
		$template_include = locate_template( array( 'single-publikacia.php' ) );
	}

	if ( ! empty( $template_include ) ) {
		return $template_include;
	}

	return $template;
}

// Handle page templates for translated pages
if ( function_exists( 'pll_current_language' ) ) {
	add_filter( 'page_template', 'scd_page_template_include' );
}
function scd_page_template_include( $template ) {

	$template_include = '';

	if ( pll_current_language() === 'en' ) {
		$xlat_id = pll_get_post( get_the_ID(), 'sk' );
		if ( ! empty( $xlat_id ) ) {
			$sk_slug = get_post_field( 'post_name', $xlat_id );
			$template_include = locate_template( array( 'page-' . $sk_slug . '.php' ) );
		}
	}

	if ( ! empty( $template_include ) ) {
		return $template_include;
	}

	return $template;
}

// Set permalink structure to pretty permalinks
add_action( 'after_switch_theme', 'scd_pretty_permalinks' );
function scd_pretty_permalinks() {
	global $wp_rewrite;
	update_option( 'rewrite_rules', false );
	$wp_rewrite->set_permalink_structure( '/%postname%/' );
	$wp_rewrite->flush_rules();
}

// Add specific classes to WordPress menu items
add_filter( 'nav_menu_css_class', 'scd_nav_menu_css_class' , 10, 4 );
function scd_nav_menu_css_class( $classes, $item, $args, $depth ) {

	if ( 'footer-quick-links' === $args->theme_location ) {
		$classes[] = 'footer-list__item';
	}

	if ( in_array( $args->theme_location, array( 'header-main', 'header-top' ) ) ) {
		$classes[] = 'main-nav__item';
	}

	return $classes;
}

// Add specific classes to WordPress menu links
add_filter( 'nav_menu_link_attributes', 'scd_nav_menu_link_attributes' , 10, 4 );
function scd_nav_menu_link_attributes( $atts, $item, $args, $depth ) {

	if ( 'footer-quick-links' === $args->theme_location ) {
		$atts['class'] = 'footer-list__link';
	}

	if ( in_array( $args->theme_location, array( 'header-main', 'header-top' ) ) ) {
		$atts['class'] = 'main-nav__link';
	}

	return $atts;
}

/**
 * Get Vite manifest data
 *
 * @return array|null
 */
function scd_get_vite_manifest() {
	static $manifest = null;
	
	if ( $manifest === null ) {
		$manifest_path = get_template_directory() . '/vite/.vite/manifest.json';
		
		if ( file_exists( $manifest_path ) ) {
			$manifest_content = file_get_contents( $manifest_path );
			$manifest = json_decode( $manifest_content, true );
		} else {
			$manifest = false;
		}
	}
	
	return $manifest;
}

/**
 * Get Vite asset URL
 *
 * @param string $entry Entry name from manifest
 * @return string|false Asset URL or false if not found
 */
function scd_get_vite_asset_url( $entry ) {
	$manifest = scd_get_vite_manifest();
	
	if ( ! $manifest || ! isset( $manifest[ $entry ] ) ) {
		return false;
	}
	
	$asset_path = $manifest[ $entry ]['file'];
	return get_template_directory_uri() . '/vite/' . $asset_path;
}

/**
 * Get Vite asset version (hash from filename)
 *
 * @param string $entry Entry name from manifest
 * @return string|null Asset version or null if not found
 */
function scd_get_vite_asset_version( $entry ) {
	$manifest = scd_get_vite_manifest();
	
	if ( ! $manifest || ! isset( $manifest[ $entry ] ) ) {
		return null;
	}
	
	$file = $manifest[ $entry ]['file'];
	// Extract hash from filename like "assets/main-l0sNRNKZ.js"
	if ( preg_match( '/[a-zA-Z0-9_-]+-([a-zA-Z0-9_-]+)\.(js|css)$/', $file, $matches ) ) {
		return $matches[1];
	}
	
	return filemtime( get_template_directory() . '/vite/' . $file );
}

// Enqueue scripts and styles.
add_action( 'wp_enqueue_scripts', 'scd_scripts' );
function scd_scripts() {
	global $wp_query;

	// Default theme stylesheet, with inline CSS
	wp_enqueue_style( 'scd', get_stylesheet_uri(), array(), SCD_VERSION );
	$inline_css = file_get_contents( get_template_directory() . '/css/inline.css' );
	if ( $inline_css ) {
		wp_add_inline_style( 'scd', $inline_css );
	}

	// Styles
	wp_enqueue_style( 'scd-swiper', get_template_directory_uri() . '/css/plugins/swiper.min.css', array(), SCD_VERSION );
// 	wp_enqueue_style( 'scd-swiper', 'https://unpkg.com/swiper@5.4.5/css/swiper.min.css', array(), null );
	wp_enqueue_style( 'scd-select2', 'https://unpkg.com/select2@4.0.13/dist/css/select2.min.css', array(), null );
	wp_enqueue_style( 'scd-bootstrap-grid', get_template_directory_uri() . '/css/plugins/bootstrap-grid.min.css', array(), SCD_VERSION );

	// Scripts
        wp_enqueue_script( 'scd-select2', get_template_directory_uri() . '/js/select2.min.js', array(), SCD_VERSION, true );
//	wp_enqueue_script( 'scd-select2', 'https://unpkg.com/select2@4.0.13/dist/js/select2.min.js', array( 'jquery' ), null, true );
	wp_enqueue_script( 'scd-swiper', get_template_directory_uri() . '/js/plugins/swiper.min.js', array(), SCD_VERSION, true );
// 	wp_enqueue_script( 'scd-swiper', 'https://unpkg.com/swiper@5.4.5/js/swiper.min.js', array(), null, true );
	wp_enqueue_script( 'scd-colcade', 'https://unpkg.com/colcade@0.2.0/colcade.js', array( 'jquery' ), null, true );

	// Load Leaflet library for Open Street Map embedding
	if ( is_singular( array( 'scd_vystava', 'scd_podujatie' ) ) 
		|| scd_is_page( array( 'kniznica', 'galeria-satelit', 'slovenske-muzeum-dizajnu', 'publikacie' ) ) ) {
		wp_enqueue_style( 'scd-leaflet', 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.css', array(), null );
		wp_enqueue_script( 'scd-leaflet', 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.js', array(), null, true );
	}

	// Vite-built assets
	$vite_css_url = scd_get_vite_asset_url( 'styles/main.css' );
	$vite_js_url = scd_get_vite_asset_url( 'main.js' );
	
	// Always load legacy assets
	wp_enqueue_style( 'scd-app', get_template_directory_uri() . '/css/app.css', array(), '20211021' );
	wp_enqueue_script( 'scd-app', get_template_directory_uri() . '/js/app.js', 
		array( 'jquery', 'scd-select2', 'scd-swiper', 'scd-colcade' ), '20221028', true );

  if ( $vite_css_url ) {
    wp_enqueue_style( 'scd-vite-main', $vite_css_url, array(), scd_get_vite_asset_version( 'styles/main.css' ) );
  }

  if ( $vite_js_url ) {
    // Vite assets need to be loaded as ES6 modules
    add_action( 'wp_footer', function() use ( $vite_js_url ) {
      echo '<script type="module" src="' . esc_url( $vite_js_url ) . '"></script>' . "\n";
    }, 99 );
  }

	// Pass arguments to AJAX scripts
	if ( is_archive() || is_home() || is_search() ) {
		// filter query vars passed to the script
		$ajax_qv = array_intersect_key( $wp_query->query_vars, array_merge( 
			array_flip( array( 
				'lang', 
				'post_type', 
				'paged', 
				'category__not_in', 
				's', 
				'kedy', 
				'edesignum', 
				'designum',
				'dizajner',
				'vyrobca',
				'rocnik',
				'hodnotenie',
				'vystava',
			) ), 
			wp_list_pluck( get_taxonomies( array( 'public' => true ), 'objects' ), 'query_var', 'query_var' ) 
		) );
		if ( $ajax_qv['post_type'] === 'scd_vystava' ) {
			$ajax_qv['typ_podujatia'] = 'vystava';
		}
		if ( $GLOBALS['SCD_CPT']->is_kalendar() ) {
			$ajax_qv['post_type'] = $GLOBALS['SCD_CPT']->kalendar_cpts;
		}
		wp_localize_script( 'scd-app', 'scd_ajax', array(
			'url' => admin_url( 'admin-ajax.php' ),
			'query_vars' => $ajax_qv,
			'current_page' => get_query_var( 'paged' ) ? get_query_var( 'paged' ) : 1,
			'max_num_pages' => $wp_query->max_num_pages,
			'current_url' => strtok( ( is_ssl() ? 'https' : 'http' ) . '://' 
				. $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'], '?' ),
			'post_type_filter' => is_post_type_archive( array( 'scd_dielo_smd', 'scd_dielo_ncd' ) ),
		) );
	}

	// Mini search
	$ms_args = array( 'ajaxurl' => admin_url( 'admin-ajax.php' ) );
	if ( function_exists( 'pll_current_language' ) ) {
		$ms_args[ 'lang' ] = pll_current_language();
	}
	wp_enqueue_script( 'scd-mini-search', get_template_directory_uri() . '/js/mini-search.js', array( 'jquery' ), '20211107', true );
	wp_localize_script( 'scd-mini-search', 'scd_mini_search', $ms_args );

	// Enqueue share script on pages that use the share component
	if ( is_singular( 'scd_dielo_ncd' ) ) {
		wp_enqueue_script( 'scd-share', get_template_directory_uri() . '/js/share.js', array( 'jquery' ), SCD_VERSION, true );
	}

	if ( has_tag( 'vsetkonajlepsiedizajn2023' ) ) {
		wp_enqueue_script( 'scd-jquery-eraser', get_template_directory_uri() . '/js/plugins/jquery.eraser.js', array( 'jquery' ), null, true );
		wp_add_inline_script( 'scd-jquery-eraser', 
			"if ( ! document.cookie.split( '; ' ).find( (row) => row.startsWith( 'vsetkonajlepsiedizajn2023' ) ) ) {
				document.cookie = 'vsetkonajlepsiedizajn2023=true; expires=Fri, 31 Dec 9999 23:59:59 GMT; path=/; Secure';

				jQuery( function( $ ) {
					var finish = function( event ) {
						if ( event) {
							event.preventDefault();
							$( '#redux' ).eraser( 'clear' );
						}
						$( '#redux' ).add( '#eraser-close' ).hide();
						document.body.style.overflowY = 'auto';
					};
					$( '#redux' ).eraser( { size: 150, completeRatio: .95, completeFunction: finish } );
					$( '#eraser-close' ).on( 'click', finish );
				} );
			}"
		);

		wp_register_style( 'scd-jquery-eraser', false );
		wp_enqueue_style( 'scd-jquery-eraser' );
		wp_add_inline_style( 'scd-jquery-eraser', "
			#redux {
				position: absolute;
				z-index: 9999992;
				width: 100%;
				height: 100%;
				cursor: url(\"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='96' height='96' viewport='0 0 100 100' style='fill:black;font-size:48px;font-family:\\\"AppleColorEmoji\\\",\\\"SegoeUIEmoji\\\",\\\"NotoColorEmoji\\\",\\\"AndroidEmoji\\\",EmojiSymbols,\\\"EmojiOneMozilla\\\",\\\"TwemojiMozilla\\\",\\\"SegoeUISymbol\\\",\\\"NotoColorEmojiCompat\\\",emoji,noto-emojipedia-fallback;'><text y='50%'>🥄</text></svg>\") 16 0, auto;
				left: 50%;
				top: 50%;
				transform: translate( -50%, -50% );
			}

			#eraser-close {
				display: none;
				position: absolute;
				z-index: 9999994;
				width: 100px;
				height: 100px;
				top: 40px;
				right: 40px;
				cursor: pointer;
			}

			@media (max-aspect-ratio: 1/1) {
				#redux path {
					display: none;
				}
				#eraser-close {
					width: 10%;
					height: auto;
					top: 7vw;
					right: 5%;
				}
			}
		" );
	}
}

function scd_enqueue_vite_dev() {
  // Also load Vite assets if available
  if (wp_get_environment_type() === 'local') {
    echo '<script type="module" src="http://localhost:5173/@vite/client"></script>';
    echo '<script type="module" src="http://localhost:5173/main.js"></script>'; // uprav cestu na tvoj entry bod!
  }
}
add_action('wp_head', 'scd_enqueue_vite_dev');

// Register custom query vars
add_filter( 'query_vars', 'scd_register_query_vars' );
function scd_register_query_vars( $vars ) {
	$vars[] = 'kedy';
	$vars[] = 'edesignum';
	$vars[] = 'designum';
	$vars[] = 'dizajner';
	$vars[] = 'vyrobca';
	$vars[] = 'rocnik';
	$vars[] = 'kategoria';
	$vars[] = 'odbor';
	$vars[] = 'hodnotenie';
	$vars[] = 'sorting';
	$vars[] = 'kiosk';
	return $vars;
}

// Modify query for custom query vars
add_action( 'pre_get_posts', 'scd_custom_vars_query' );
function scd_custom_vars_query( $query ) {

	// Prevent recursion caused by sub-queries
	remove_action( 'pre_get_posts', 'scd_custom_vars_query' );

	if ( ! wp_doing_ajax() && ( is_admin() || ! $query->is_main_query() ) ) {
		return;
	}

	if ( wp_doing_ajax() && $_POST['action'] !== 'loadmore' ) {
		return;
	}

	$mq = array();
	$tq = array();

	if ( $query->is_home() || $query->is_category() || $query->is_tag() 
			|| ( wp_doing_ajax() && empty( $query->get( 'post_type' ) ) ) ) {
		if ( ! is_null( get_query_var( 'edesignum', null ) ) ) {
			$mq[] = array( 'key' => 'edesignum', 'value' => '1' );
		}
		if ( ! empty( $designum = get_query_var( 'designum' ) ) ) {
			$mq[] = array( 'key' => 'vydanie_designum', 'value' => $designum );
		} elseif ( ! is_null( get_query_var( 'designum', null ) ) ) {
			$mq[] = array( 'key' => 'vydanie_designum', 'value' => '', 'compare' => '!=' );
		}
	}

	if ( $query->is_post_type_archive( array( 'scd_dielo_smd', 'scd_dielo_ncd' ) ) ) {
		if ( ! empty( $dizajner = get_query_var( 'dizajner' ) ) ) {
			add_filter( 'posts_where', 'scd_wildcard_meta_query' );
			$mq[] = array( 'key' => 'dizajner_rola_*_dizajner', 'value' => $dizajner );
		}
		if ( ! empty( $vyrobca = get_query_var( 'vyrobca' ) ) ) {
			add_filter( 'posts_where', 'scd_wildcard_meta_query' );
			$mq[] = array( 'key' => 'vyrobca_rola_*_vyrobca', 'value' => $vyrobca );
		}
		if ( ! empty( $rocnik = get_query_var( 'rocnik' ) ) ) {
			$tq[] = array( 'taxonomy' => 'scd_ncd_rocnik', 'field' => 'slug', 'terms' => $rocnik );
		}
		if ( ! empty( $kategoria = get_query_var( 'kategoria' ) ) ) {
			$tq[] = array( 'taxonomy' => 'scd_ncd_kategoria', 'field' => 'slug', 'terms' => $kategoria );
		}
		if ( ! empty( $odbor = get_query_var( 'odbor' ) ) ) {
			$tq[] = array( 'taxonomy' => 'scd_ncd_kategoria', 'field' => 'slug', 'terms' => $odbor );
		}
		if ( ! empty( $hodnotenie = get_query_var( 'hodnotenie' ) ) ) {
			if ( in_array( 'ocenene', $hodnotenie ) ) {
				$ocenene = array_diff( get_terms( array(
					'taxonomy' => 'scd_ocenenie',
					'fields' => 'id=>name',
			  	) ), array( 'nominované', 'vystavené', 'nepostupujúce do druhého kola', 'postupujúce do druhého kola' ) ); // TODO translate terms
				$tq[] = array( 
					'taxonomy' => 'scd_ocenenie', 
					'terms' => array_keys( $ocenene ),
					'operator' => 'IN',
				);
				$hodnotenie = array_diff( $hodnotenie, array( 'ocenene' ) );
			}
			if ( ! empty( $hodnotenie ) ) {
				$tq[] = array( 'taxonomy' => 'scd_ocenenie', 'field' => 'slug', 'terms' => $hodnotenie ); // TODO translate terms
			}
		}
	} elseif ( $query->is_post_type_archive( 'scd_osobnost_dizajnu' ) ) {
		if ( ! empty( get_query_var( 'vystava' ) ) ) {

			$vystava = current( get_posts( array(
				'post_status' => 'publish',
				'post_type' => 'scd_vystava',
				'name' => get_query_var( 'vystava' ),
			) ) );

			if ( ! empty( $vystava ) ) {

				$ids = array();

				$autori_roly = get_field( 'vystavujuci_autori_roly', $vystava );
				if ( ! empty( $autori_roly ) ) {
					$ids = wp_list_pluck( array_filter( wp_list_pluck( $autori_roly, 'osobnost' ) ), 'ID' );
				}

				$vyrobcovia = get_field( 'vyrobcovia_firmy_skoly', $vystava );
				if ( ! empty( $vyrobcovia ) ) {
					$ids = array_merge( $ids, array_filter( wp_list_pluck( $vyrobcovia, 'ID' ) ) );
				}

				$query->set( 'post__in', $ids );
			}
		}
	}

	if ( ! empty( $mq ) ) {
		$query->set( 'meta_query', $mq );
	}

	if ( ! empty( $tq ) ) {
		$query->set( 'tax_query', $tq );
	}

	add_action( 'pre_get_posts', 'scd_custom_vars_query' );
}

// Handle AJAX load more and filter requests
add_action( 'wp_ajax_loadmore', 'scd_ajax_loadmore' );
add_action( 'wp_ajax_nopriv_loadmore', 'scd_ajax_loadmore' );
function scd_ajax_loadmore() {
	global $SCD_CPT, $wp_query;

	$args = filter_var_array( $_POST['query'], FILTER_SANITIZE_STRING );

	// Filter allowed post types
	$allowed_cpts = array( 
		'', 
		'post', 
		'scd_vystava', 
		'scd_podujatie', 
		'scd_osobnost_dizajnu', 
		'scd_hra', 
		'scd_dielo_smd', 
		'scd_dielo_ncd',
		'scd_designum',
	);

	if ( ! empty( array_diff( (array) $args['post_type'], $allowed_cpts ) ) ) {
		wp_die();
	}

	$args['nopaging'] = false;
	$args['paged'] = filter_input( INPUT_POST, 'page', FILTER_SANITIZE_NUMBER_INT ) + 1;
	$args['post_status'] = 'publish';
	$args['ignore_sticky_posts'] = true;
// 	$args['post__not_in'] = explode( ',', $_POST['exclude'] );

	// Handle sorting parameter
	if ( ! empty( $args['sorting'] ) ) {
		$sorting_type = $args['sorting'];
		
		switch ( $sorting_type ) {
			case 'newest':
				$args['orderby'] = 'date';
				$args['order'] = 'DESC';
				unset( $args['sorting'] );
				break;
			case 'oldest':
				$args['orderby'] = 'date';
				$args['order'] = 'ASC';
				unset( $args['sorting'] );
				break;
			case 'relevance':
				// Use relevance sorting which now implements hierarchical sorting by categories
				$relevance_sorter = SCD_Relevance_Sorter::get_instance();
				if ( $relevance_sorter->should_apply_relevance_sorting( $args ) ) {
					$args = $relevance_sorter->apply_relevance_query( $args );
				} else {
					// Fallback to default WordPress relevance for search queries
					if ( ! empty( $args['s'] ) ) {
						$args['orderby'] = 'relevance';
					}
					unset( $args['sorting'] );
				}
				break;
			// For 'normal' and other values, use default sorting
			default:
				unset( $args['sorting'] );
				break;
		}
	}

	if ( isset( $args['tag'] ) && $args['tag'] === '0' ) {
		unset( $args['tag'] );
	}

	$args = $GLOBALS['SCD_CPT']->adjust_kalendar_request( $args );

	query_posts( $args );

	if ( $SCD_CPT->is_kalendar() ) {
		$template = 'kalendar';
		$notfound = esc_html__( 'Ľutujeme, ale nenašli sme žiadne výstavy a podujatia.', 'scd' );
	} elseif ( is_post_type_archive( 'scd_osobnost_dizajnu' ) ) {
		$template = 'osobnost';
		$notfound = esc_html__( 'Ľutujeme, ale nenašli sme žiadne osobnosti dizajnu.', 'scd' );
	} elseif ( is_post_type_archive( 'scd_hra' ) ) {
		$template = 'hra';
		$notfound = esc_html__( 'Ľutujeme, ale nenašli sme žiadne hry.', 'scd' );
	} elseif ( is_post_type_archive( array( 'scd_dielo_smd', 'scd_dielo_ncd' ) ) ) {
		$template = 'dielo';
		$notfound = esc_html__( 'Ľutujeme, ale nenašli sme žiadne diela.', 'scd' );
	} elseif ( is_post_type_archive( 'scd_designum' ) ) {
		$template = 'designum';
		$notfound = esc_html__( 'Ľutujeme, ale nenašli sme žiadne vydania Designum.', 'scd' );
	} else {
		$template = 'post';
		$notfound = esc_html__( 'Ľutujeme, ale nenašli sme žiadne články.', 'scd' );
	}

	ob_start();
	
	// Check if we're using relevance sorting (which now implements hierarchical sorting)
	if ( ! empty( $_POST['query']['sorting'] ) && $_POST['query']['sorting'] === 'relevance' ) {
		// Use relevance display logic for relevance sorting
		$relevance_sorter = SCD_Relevance_Sorter::get_instance();
		$original_args = filter_var_array( $_POST['query'], FILTER_SANITIZE_STRING );
		echo $relevance_sorter->get_ajax_relevance_data( $original_args );
	} else {
		// Use standard display logic
		if ( have_posts() ) {
			while ( have_posts() ) {
				the_post();
				get_template_part( 'template-parts/archive', $template );
			}
		} else {
			echo '<p>'; echo $notfound; echo '</p>';
		}
	}

	wp_send_json( array( 'html' => ob_get_clean(), 'max_num_pages' => $wp_query->max_num_pages ) );
}

// Modify post excerpt look
add_filter( 'excerpt_length', 'scd_excerpt_length' );
function scd_excerpt_length( $length ) {
	return 18;
}
add_filter( 'excerpt_more', 'scd_excerpt_more' );
function scd_excerpt_more( $more ) {
	return '&hellip;';
}

/**
 * Check whether current request is for any of registered mini home pages
 *
 * @return bool
 */
function scd_is_mini_home_page() {
	global $SCD_CPT;

	$slugs = wp_list_pluck( $SCD_CPT->mini_home_pages, 'post_name' );
	return is_page( $slugs );
}

/**
 * Extend get_terms() with ability to query terms only within particular post type
 *
 * @param array|string $args       Optional. Array or string of arguments. See WP_Term_Query::__construct()
 *                                 for information on accepted arguments. Default empty array.
 * @return WP_Term[]|int[]|string[]|string|WP_Error Array of terms, a count thereof as a numeric string,
 *                                                  or WP_Error if any of the taxonomies do not exist.
 *                                                  See the function description for more information.
 */
function scd_get_terms( $args = array() ) {
	$args = wp_parse_args( $args );

	if ( ! empty( $args['post_types'] ) ) {
		$args['post_types'] = (array) $args['post_types'];
		add_filter( 'terms_clauses', 'scd_filter_terms_by_cpt', 10, 3 );
	}

	return get_terms( $args );
}

// See scd_get_terms() above
function scd_filter_terms_by_cpt( $pieces, $tax, $args ) {
	global $wpdb;
	
	$placeholders = implode( ',', array_fill( 0, count( $args['post_types'] ), '%s' ) );
	
	$pieces['fields'] .=", COUNT(*) ";
	$pieces['join'] .=" INNER JOIN $wpdb->term_relationships AS r ON r.term_taxonomy_id = tt.term_taxonomy_id INNER JOIN $wpdb->posts AS p ON p.ID = r.object_id ";
	$pieces['where'] .= $wpdb->prepare( " AND p.post_type IN($placeholders) GROUP BY t.term_id", $args['post_types'] );
	
	remove_filter( 'terms_clauses', 'scd_filter_terms_by_cpt' );
	return $pieces;
}

/**
 * Query $num future events or latest past ones, ordered by date
 *
 * @param array $args
 * @param boolean $future_only Don't return past events in case no enough future events exist
 * @return WP_Post[]|int[] Array of post objects or post IDs.
 */
function scd_event_query( $args = array(), bool $future_only = false ) {

	$defaults = array(
		'numberposts' => 3,
		'post_status' => 'publish',
		'post_type' => 'scd_vystava',
		'meta_query' => array(),
		'meta_key' => 'trvanie_od',
		'orderby' => 'meta_value',
		'order' => 'ASC',
	);
	$meta_defaults = array( array(
		array(
			'key' => 'trvanie_od',
			'value' => current_time( 'Ymd' ),
			'compare' => '>=',
			'type' => 'DATE',
		),
	) );

	$args = wp_parse_args( $args, $defaults );
	$args['meta_query'] = wp_parse_args( $args['meta_query'], $meta_defaults );

	$posts = get_posts( $args );

	if ( count( $posts ) < $args['numberposts'] ) {
		$args['numberposts'] = $args['numberposts'] - count( $posts );
		$args['order'] = 'DESC';
		$args['meta_query'][0][0]['compare'] = '<';
		if ( $future_only ) {
			$args['meta_query'][0]['relation'] = 'AND';
			$args['meta_query'][0][1] = array(
				'key' => 'trvanie_do',
				'value' => current_time( 'Ymd' ),
				'compare' => '>=',
				'type' => 'DATE',
			);
		}

		$posts = array_merge( array_reverse( get_posts( $args ) ), $posts );
	}

	return $posts;
}

/**
 * Get value of first matched non-empty ACF field
 *
 * @param array $selectors
 * @param WP_Post|int $post
 * @return void|false|array
 */
function scd_get_fields( array $selectors, $post = null ) {
	$fields = get_fields( $post );
	foreach ( $selectors as $selector ) {
		if ( isset( $fields[ $selector ] ) ) {
			return $fields[ $selector ];
		}
	}
	return null;
}

/**
 * Return HTML for CPT title, optionally wrapped in permalink
 *
 * @param WP_Post|int $post
 * @param string $link_class
 * @return string
 */
function scd_get_linked_title( $post, $link_class = '' ) {
	$out = '';
	if ( ! empty( $post ) ) {
		$out = esc_html( get_the_title( $post ) );
		if ( get_post_status( $post ) == 'publish' || is_user_logged_in() ) {
			$out = '<a href="' . esc_url( get_permalink( $post ) ) . '"' 
				. ( empty( $link_class ) ? '' : ' class="' . $link_class . '"' ) . '>' . $out . '</a>';
		}
	}
	return $out;
}

// Handle MailChimp subscribe AJAX action
// add_action( 'wp_ajax_nopriv_mailchimp_subscribe', 'scd_ajax_mailchimp_subscribe' );
// add_action( 'wp_ajax_mailchimp_subscribe', 'scd_ajax_mailchimp_subscribe' );
function scd_ajax_mailchimp_subscribe() {
	require plugin_dir_path( __FILE__ ) . 'vendor/autoload.php';

	$key = '************************************';
	$list = '0b5c8e6406';

	$mailchimp = new MailChimp( $key );
	$result = $mailchimp->post( "lists/$list/members", array( 
		'email_address' => filter_input( INPUT_POST, 'email', FILTER_SANITIZE_STRING ),
		'status' => 'subscribed',
	) );

	$response = array( 'status' => $result['status'] );
	if ( isset( $result['title'] ) ) {
		$response['title'] = $result['title'];
	}
	wp_send_json( $response );
}

// Keep original publishing date of imported articles
add_action( 'pending_to_publish', 'scd_keep_original_date' );
function scd_keep_original_date( $post ) {
	$original = get_post_meta( $post->ID, 'original_date', true );
	if ( ! empty( $original ) ) {
		wp_update_post( array (
			'ID' => $post->ID,
			'post_date' => $original,
			'post_date_gmt' => get_gmt_from_date( $original ),
		) );
	}
}

/**
 * Filter to extend meta query functionality to allow meta_key compare LIKE with arbitrary wildcard position.
 * Replaces '*' with '%' in modified query SQL. Hook it on 'posts_where' filter.
 *
 * @param string $where
 * @return string
 */
function scd_wildcard_meta_query( $where ) {
	return strpos( $where, '*' ) !== false ? 
		str_replace( array( 'meta_key =', '*' ), array( 'meta_key LIKE', '%' ), $where ) : $where;
}

/**
 * Constructs WP meta query array for querying PHP serialized arrays for multiple IDs
 *
 * @param string $key
 * @param array $values
 * @return array
 */
function scd_get_in_array_meta_query( string $key, array $values ) {
	$mq = array();
	if ( ! empty( $values ) ) {
		$mq = array( 'relation' => 'OR' );
		foreach ( $values as $value ) {
			$mq[] = array( 'key' => $key, 'value' => '"' . $value . '"', 'compare' => 'LIKE' );
		}
	}
	return $mq;
}

// Limit search to specific post types only
add_filter( 'pre_get_posts', 'scd_limit_search' );
function scd_limit_search( $query ) {
 	if ( $query->is_search && ! is_admin() ) {
		$query->set( 'post_type', array( 'post', 'scd_vystava', 'scd_podujatie' ) );
	}
	return $query;
}

// AJAX mini search
add_action( 'wp_ajax_nopriv_scd_mini_search', 'scd_mini_search' );
add_action( 'wp_ajax_scd_mini_search', 'scd_mini_search' );
function scd_mini_search() {

	add_filter( 'posts_where', 'scd_where_title_like' );
	$results = get_posts( array(
		'suppress_filters' => false,
		'numberposts' => 8,
		'post_status' => 'publish',
		'post_type' => array( 
			'page',
			'post',
			'scd_vystava',
			'scd_podujatie',
			'scd_publikacia',
			'scd_pub_kniznice',
			'scd_designum',
			'scd_autor',
			'scd_osobnost_dizajnu',
			'scd_dielo_ncd',
			'scd_dielo_smd',
		),
	) );
	remove_filter( 'posts_where', 'scd_where_title_like' );

	$out = array();

	foreach ( $results as $post ) {
		$out[] = array(
				"title" => get_the_title( $post ),
				"slug" => get_permalink( $post ),
		);
	}

	wp_send_json( $out );
}

// Mini search by title filter
function scd_where_title_like( $where ) {
	global $wpdb;
	$search = wp_unslash( sanitize_post_field( 'post_title', $_POST['s'], 0, 'db' ) );
	$where .= $wpdb->prepare( " AND {$wpdb->posts}.post_title LIKE %s", '%' . $wpdb->esc_like( $search ) . '%' );
	return $where;
}

// Deregister unnecessary image sizes
add_action( 'init', 'scd_remove_image_sizes' );
function scd_remove_image_sizes() {
	remove_image_size( '1536x1536' );
	remove_image_size( '2048x2048' );
}

// Allowed file types for uploading
add_filter( 'upload_mimes', 'scd_mime_types' );
function scd_mime_types( $mimes ) {
	$mimes['jfif'] = 'image/jpeg';
	unset( $mimes['tiff|tif'] );
	return $mimes;
}

// Allow jfif files upload
add_filter( 'wp_check_filetype_and_ext', 'scd_check_filetype_and_ext', 10, 4 );
function scd_check_filetype_and_ext( $types, $file, $filename, $mimes ) {
	if ( false !== strpos( $filename, '.jfif' ) ) {
		$types['ext'] = 'jfif';
		$types['type'] = 'image/jpeg';
	}
	return $types;
}

// Modify 'posts_per_page' for specific CPT archive pages
add_filter( 'pre_get_posts', 'scd_posts_per_page' );
function scd_posts_per_page( $query ) {
	if ( ! wp_doing_ajax() && ( is_admin() || ! $query->is_main_query() ) ) {
		return;
	}

	if ( wp_doing_ajax() && $_POST['action'] !== 'loadmore' ) {
		return;
	}

	if ( $query->is_post_type_archive( 
			array( 'scd_osobnost_dizajnu', 'scd_dielo_smd', 'scd_dielo_ncd', 'scd_hra', 'scd_designum' ) ) ) {
		$query->set( 'posts_per_page', 12 );
	}
}

/**
 * Get translated post ID by post or post ID
 *
 * @param WP_Post|int $post
 * @return int
 */
function scd_get_translated_post_id( $post ) {

	if ( function_exists( 'pll_get_post' ) ) {
		$id = is_object( $post ) ? $post->ID : $post;
		$post = pll_get_post( $id );
	}

	return is_object( $post ) ? $post->ID : $post;
}

/**
 * Recursive empty() function
 *
 * @param mixed $var
 * @return mixed
 */
function scd_empty_deep( $var ) {
	if ( is_array( $var ) ) {
		foreach ( $var as $value ) {
			if ( ! scd_empty_deep( $value ) ) {
				return false;
			}
		}
		return true;
	} else {
		return empty( $var );
	}
}

// Enable Polylang synchronization only for some ACF post metas, mainly on new translation creation
add_filter( 'pll_copy_post_metas', 'scd_pll_copy_post_metas', 10, 2 );
function scd_pll_copy_post_metas( $metas, $sync ) {

	$add_metas = array(
		'id',
		'trvanie_od',
		'trvanie_do',
		'porotca',
	);

	// copying to new translation, not syncing
	if ( $sync === false ) {
		$add_metas = array_merge( $add_metas, array(
			'krajina',
			'perex',
			'zdroj',
			'zdroj_url',
			'galeria',
			'datum_vydania',
			'cena',
			'datovanie',
			'obrazky',
			'rozmery',
			'vyska',
			'sirka',
			'hlbka',
			'clanky_a_recenzie',
			'kopia_hry',
			'datum_zaciatku',
			'datum_konca',
			'vstupne',
			'anotacia',
			'lokalita_text',
			'kurator_text',
			'prilohy',
		) );
	}

	return array_merge( $metas, $add_metas );
}

/**
 * Extended is_page() with checking for English page translations
 *
 * @param string $pages
 * @return bool
 */
function scd_is_page( $pages = '' ) {

	if ( ! empty( $pages ) && function_exists( 'pll_get_post' ) ) {

		$translations = array();

		if ( ! is_array( $pages ) ) {
			$pages = array( $pages );
		}

		foreach ( $pages as $page ) {
			if ( ! is_numeric( $page ) ) {
				$post = get_page_by_path( $page );
				if ( is_object( $post ) ) {
					$post_id = $post->ID;
				}
			} else {
				$post_id = $page;
			}
			if ( ! empty( $post_id ) && ! empty ( $translated = pll_get_post( $post_id, 'en' ) ) ) {
				$translations[] = get_post_field( is_numeric( $page ) ? 'ID' : 'post_name', $translated );
			}
		}

		$pages = array_merge( $pages, $translations );
	}

	return is_page( $pages );
}

// Order ACF post field search results by relevance
add_filter( 'acf/fields/post_object/query', 'scd_acf_post_object_field_orderby' );
function scd_acf_post_object_field_orderby( $args ) {
	if ( isset( $args['s'] ) ) {
		$args['orderby'] = 'relevance';
	}
	return $args;
}

/**
 * Get taxonomy archive link
 *
 * @param string $taxonomy
 * @param string $path
 * @return string
 */
function scd_get_taxonomy_archive_link( $taxonomy, $path = '' ) {
	$tax = get_taxonomy( $taxonomy );
	$link = get_bloginfo( 'url' ) . '/' . $tax->rewrite['slug'] . $path;
	if ( scd_is_kiosk() ) {
		return add_query_arg( 'kiosk', '', $link );
	}
	return $link;
}

// Disable default 'Uncategorized' category for posts
add_filter( 'pre_option_default_category', '__return_empty_string', 1000 );

/**
 * Checks whether we are in kiosk mode
 *
 * @return bool
 */
function scd_is_kiosk() {
	return ( ! is_admin() || wp_doing_ajax() ) && ( isset( $_REQUEST['kiosk'] ) || isset( $_COOKIE['kiosk'] ) );
}

// Set kiosk cookie
add_action( 'init', 'scd_set_kiosk_cookie' );
function scd_set_kiosk_cookie() {
	if ( scd_is_kiosk() && ! isset( $_COOKIE['kiosk'] ) ) {
		setcookie( 'kiosk', 1, 0, '/' );
	}
}

// Add kiosk query var to internal links when in kiosk mode
add_filter( 'post_type_archive_link', 'scd_kiosk_link_filter', 100 );
add_filter( 'post_type_link', 'scd_kiosk_link_filter', 100 );
add_filter( 'post_link', 'scd_kiosk_link_filter', 100 );
add_filter( 'page_link', 'scd_kiosk_link_filter', 100 );
add_filter( 'term_link', 'scd_kiosk_link_filter', 100 );
function scd_kiosk_link_filter( $link ) {
	if ( scd_is_kiosk() ) {
		return add_query_arg( 'kiosk', '', $link );
	}
	return $link;
}

/**
 * Return true if URL is within this website domain
 *
 * @param string $url
 * @return bool
 */
function scd_is_internal_url( $url ) {
	return wp_parse_url( $url, PHP_URL_HOST ) === wp_parse_url( home_url(), PHP_URL_HOST );
}

/**
 * Sanitize URL while in kiosk mode
 *
 * @param string $url
 * @return string
 */
function scd_kiosk_url( $url ) {
	return scd_is_kiosk() ? ( scd_is_internal_url( $url ) ? scd_kiosk_link_filter( $url ) : '#0' ) : $url;
}

// Apply kiosk links to the posts content
add_filter( 'the_content', 'scd_kiosk_content', 100 );
function scd_kiosk_content( $content ) {

	if ( ! scd_is_kiosk() ) {
		return $content;
	}

	$content = preg_replace( '#\starget=[\'"]_blank[\'"]#mi', '', $content );

	return preg_replace_callback( 
		'#\shref=[\'"]([^\'"]*?)[\'"]#mi', 
		function ( $matches ) {
			return ' href="' . scd_kiosk_url( $matches[1] ) . '"'; 
		},
		$content
	);
}

// Don't index and don't follow kiosk mode pages
add_filter( 'wpseo_robots', 'scd_kiosk_robots', 10, 2 );
function scd_kiosk_robots( $output, $presentation ) {
	if ( scd_is_kiosk() ) {
		return preg_replace( 
			array( '#\bindex\b#sm', '#\bfollow\b#sm' ), 
			array( 'noindex', 'nofollow' ),
			$output
		);
	}
	return $output;
}

// Print full hierarchical term path in admin column
add_filter( 'post_column_taxonomy_links', 'scd_dielo_kolekcia_column', 10, 3 );
function scd_dielo_kolekcia_column( $term_links, $taxonomy, $terms ) {
	if ( $taxonomy === 'scd_kolekcia' ) {
		foreach ( $terms as $i => $term ) {
			$name = $term->name;
			$hierarchy = $term->name;
			while ( $term->parent ) {
				$term = get_term( $term->parent, 'scd_kolekcia' );
				$hierarchy = $term->name . ' > ' . $hierarchy;
			}
			$term_links[ $i ] = str_replace( '>' . $name . '<', '>' . $hierarchy . '<', $term_links[ $i ] );
		}
	}
	return $term_links;
}

// Define URLs of collection terms assigned to exhibitions
add_filter( 'term_link', 'scd_kolekcia_term_link', 10, 3 );
function scd_kolekcia_term_link( $url, $term, $taxonomy ) {
	if ( $taxonomy === 'scd_kolekcia' ) {
		$exhibitions = get_posts( array(
			'post_type' => 'scd_vystava',
			'tax_query' => array( array(
				'taxonomy' => 'scd_kolekcia',
				'terms' => get_ancestors( $term->term_id, 'scd_kolekcia', 'taxonomy' ),
			) ),
		) );
		if ( ! empty( $exhibitions ) ) {
			$url = add_query_arg( 'kolekcia', $term->slug, get_permalink( current( $exhibitions ) ) );
		}
	}
	return $url;
}

// Custom taxonomy scd_osobnost_typ term link
add_filter( 'term_link', 'scd_osobnost_typ_term_link', 10, 3 );
function scd_osobnost_typ_term_link( $url, $term, $taxonomy ) {
	if ( $taxonomy === 'scd_osobnost_typ' ) {
		$url = add_query_arg( 'typ_osobnosti', $term->slug, get_post_type_archive_link( 'scd_osobnost_dizajnu' ) );
	}
	return $url;
}

// HACK Don't allow WordPress parse_request() to override post_type set by rewrite rules
add_filter( 'request', 'scd_restore_post_type_set_by_rewrite_rules' );
function scd_restore_post_type_set_by_rewrite_rules( $request ) {
	global $wp;

	if ( ! empty( $wp->matched_query ) && strpos( $wp->matched_query, 'post_type' ) !== false ) {
		wp_parse_str( $wp->matched_query, $rewrite_vars );

		// Allow overriding if post_type is present in query string
		if ( isset( $_REQUEST['post_type'] ) ) {
			return $request;
		}

		if ( $rewrite_vars['post_type'] !== $request['post_type'] ) {
			$request['post_type'] = $rewrite_vars['post_type'];
			unset( $request['name'] );
		}
	}

	return $request;
}

// Remove custom taxonomy scd_kolekcia metabox from scd_vystava CPT edit screen 
// Only rely on ACF input field which is able to limit number of asssigned terms to one
add_action( 'add_meta_boxes', 'scd_remove_collection_metabox_from_exhibition', 20, 2 );
function scd_remove_collection_metabox_from_exhibition( $post_type, $post ) {
	remove_meta_box( 'scd_kolekciadiv', 'scd_vystava', 'side' );
}

// Ignore version control (Git) in the context of automatic updates
add_filter( 'automatic_updates_is_vcs_checkout', '__return_false' );

// Enable minor automatic WP core updates
add_filter( 'allow_dev_auto_core_updates', '__return_false' );
add_filter( 'allow_minor_auto_core_updates', '__return_true' );
add_filter( 'allow_major_auto_core_updates', '__return_false' );

add_action( 'wp_body_open', 'scd_include_vsetkonajlepsiedizajn2023_modal' );
function scd_include_vsetkonajlepsiedizajn2023_modal() {
	if ( has_tag( 'vsetkonajlepsiedizajn2023' ) ) {
		get_template_part( 'template-parts/modal', 'vsetkonajlepsiedizajn2023' );
	}
}


/**
 * Extract and transform a YouTube video URL into an embeddable format.
 *
 * @param  string  $url  The YouTube video URL to parse.
 *
 * @return string The embeddable YouTube video URL or an empty string if the URL cannot be parsed.
 */
function get_youtube_embed_url(string $url): string
{
  if (empty($url)) {
    return '';
  }

  $patterns = [
    '/[\\?\\&]v=([^\\?\\&]+)/',           // Standard YouTube URL
    '/youtu\.be\/([^\\?\\&]+)/',          // Shortened YouTube URL
    '/youtube\.com\/embed\/([^\\?\\&]+)/', // Embed URL
    '/youtube\.com\/v\/([^\\?\\&]+)/'      // Legacy embed URL
  ];

  foreach ($patterns as $pattern) {
    if (preg_match($pattern, $url, $matches)) {
      return 'https://www.youtube.com/embed/' . $matches[1];
    }
  }

  return '';
}

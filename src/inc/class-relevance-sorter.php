<?php
/**
 * Relevance-based sorting functionality (implements hierarchical sorting by categories)
 * 
 * This class implements relevance sorting by organizing works hierarchically,
 * grouped by categories (odbor) from scd_ncd_kategoria taxonomy, with both 
 * categories and works sorted alphabetically.
 * 
 * @package scd
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly
}

class SCD_Relevance_Sorter {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize hooks if needed
    }
    
    /**
     * Get a singleton instance of this class
     * 
     * @return SCD_Relevance_Sorter
     */
    public static function get_instance() {
        static $instance = null;
        if ( null === $instance ) {
            $instance = new self();
        }
        return $instance;
    }
    
    /**
     * Check if current request is using relevance sorting
     * 
     * @return bool
     */
    public static function is_relevance_sorting() {
        return get_query_var( 'sorting' ) === 'relevance' || 
               ( isset( $_POST['query']['sorting'] ) && $_POST['query']['sorting'] === 'relevance' );
    }
    
    /**
     * Check if relevance sorting should be applied
     * 
     * @param array $args WP_Query arguments
     * @return bool True if relevance sorting should be applied
     */
    public function should_apply_relevance_sorting( $args ) {
        // Check if this is a relevance sorting request
        return isset( $args['sorting'] ) && $args['sorting'] === 'relevance';
    }
    
    /**
     * Apply relevance-based query modifications
     * 
     * This method modifies the query args to return an empty result set
     * since we'll handle the display logic separately for relevance sorting.
     * 
     * @param array $args Original WP_Query arguments
     * @return array Modified arguments that return no posts
     */
    public function apply_relevance_query( $args ) {
        // Remove the sorting parameter as it's not a valid WP_Query parameter
        unset( $args['sorting'] );
        
        // Set query to return no posts since we'll handle display separately
        $args['post__in'] = array( 0 ); // This will return no posts for separate handling
        
        return $args;
    }
    
    /**
     * Get works data for AJAX response
     * 
     * This method is used in AJAX requests to return relevance data
     * 
     * @param array $args Query arguments
     * @return array Relevance works data
     */
    public function get_ajax_relevance_data( $args ) {
        $relevance_data = $this->get_relevance_works( $args );
        
        ob_start();
        
        if ( ! empty( $relevance_data ) ) {
            foreach ( $relevance_data as $group ) {
                // Category header as a full-width column
                echo '<div class="hierarchical-category-group col-12 margin-bottom-2">';
                echo '<h3 class="hierarchical-category-title">' . esc_html( $group['category']->name ) . '</h3>';
                echo '</div>';
                
                // Work items as direct children of the main grid
                foreach ( $group['works'] as $work ) {
                    // Set up global post data
                    $GLOBALS['post'] = $work;
                    setup_postdata( $work );
                    
                    // Use the existing template part (it has its own col-* classes)
                    get_template_part( 'template-parts/archive', 'dielo' );
                }
            }
            
            wp_reset_postdata();
        } else {
            echo '<p>' . esc_html__( 'Ľutujeme, ale nenašli sme žiadne diela.', 'scd' ) . '</p>';
        }
        
        return ob_get_clean();
    }
    
    /**
     * Get works organized by relevance (hierarchically by categories)
     * 
     * This method retrieves all works grouped by the 7 odbor categories,
     * sorts categories alphabetically, and sorts works within each category
     * alphabetically by title.
     * 
     * @param array $args Original WP_Query arguments
     * @return array Relevance structure with categories and works
     */
    public function get_relevance_works( $args ) {
        // Remove the sorting parameter as it's not a valid WP_Query parameter
        unset( $args['sorting'] );
        
        // Get all categories from the taxonomy
        $categories = get_terms( array(
            'taxonomy' => 'scd_ncd_kategoria',
            'hide_empty' => true,
            'orderby' => 'name',
            'order' => 'ASC',
            'slug' => SCD_Config::get_odbor_terms(), // Only get the 7 specific odbor terms
        ) );
        
        if ( is_wp_error( $categories ) || empty( $categories ) ) {
            return array();
        }
        
        $relevance_data = array();
        
        // Process each category
        foreach ( $categories as $category ) {
            // Modify args to query works for this specific category
            $category_args = $args;
            $category_args['tax_query'] = array(
                array(
                    'taxonomy' => 'scd_ncd_kategoria',
                    'field' => 'slug',
                    'terms' => $category->slug,
                ),
            );
            
            // If there are additional tax_query conditions from the original args, merge them
            if ( isset( $args['tax_query'] ) && is_array( $args['tax_query'] ) ) {
                $category_args['tax_query']['relation'] = 'AND';
                $category_args['tax_query'] = array_merge( $category_args['tax_query'], $args['tax_query'] );
            }
            
            // Set sorting to alphabetical by title
            $category_args['orderby'] = 'title';
            $category_args['order'] = 'ASC';
            $category_args['posts_per_page'] = -1; // Get all works for this category
            
            // Query works for this category
            $works_query = new WP_Query( $category_args );
            
            if ( $works_query->have_posts() ) {
                $relevance_data[] = array(
                    'category' => $category,
                    'works' => $works_query->posts,
                    'work_count' => $works_query->found_posts,
                );
            }
            
            // Clean up
            wp_reset_postdata();
        }
        
        // After processing all 7 main odbor categories, add "Ostatné" section
        // Query for works that don't belong to any of the main odbor categories
        $ostatne_args = $args;
        $ostatne_args['tax_query'] = array(
            array(
                'taxonomy' => 'scd_ncd_kategoria',
                'field' => 'slug',
                'terms' => SCD_Config::get_odbor_terms(),
                'operator' => 'NOT IN',
            ),
        );

        // If there are additional tax_query conditions from the original args, merge them
        if ( isset( $args['tax_query'] ) && is_array( $args['tax_query'] ) ) {
            $ostatne_args['tax_query']['relation'] = 'AND';
            $ostatne_args['tax_query'] = array_merge( $ostatne_args['tax_query'], $args['tax_query'] );
        }

        // Set sorting to alphabetical by title
        $ostatne_args['orderby'] = 'title';
        $ostatne_args['order'] = 'ASC';
        $ostatne_args['posts_per_page'] = -1; // Get all uncategorized works

        // Query works that don't belong to main odbor categories
        $ostatne_query = new WP_Query( $ostatne_args );

        if ( $ostatne_query->have_posts() ) {
            // Create a mock category object for "Ostatné"
            $ostatne_category = new stdClass();
            $ostatne_category->name = 'Ostatné';
            $ostatne_category->slug = 'ostatne';
            $ostatne_category->term_id = 0; // Use 0 as a special ID for this virtual category
            
            $relevance_data[] = array(
                'category' => $ostatne_category,
                'works' => $ostatne_query->posts,
                'work_count' => $ostatne_query->found_posts,
            );
        }

        // Clean up
        wp_reset_postdata();
        
        return $relevance_data;
    }
}

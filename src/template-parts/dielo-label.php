<?php
/**
 * Template part for displaying dielo labels based on "nove_hodnotenie" field
 * This template handles different variations of labels
 */

$nove_hodnotenie = get_field('nove_hodnotenie');

// Only render if nove_hodnotenie field exists and has a value
if ($nove_hodnotenie && !empty($nove_hodnotenie['value'])) {
    
    // Check if it's nominacia - render the current label
    if ($nove_hodnotenie['value'] === 'nominacia') {
        ?>
        <div class="absolute bottom-0 left-0 w-full bg-[#EFEFEF] text-black flex justify-between items-center p-3 z-10">
            <strong><?php echo esc_html($nove_hodnotenie['label']); ?></strong>
            <strong><?php echo date('Y'); ?></strong>
        </div>
        <?php
    }
    
    // Check if it's hlavna-cena - render label with trophy icon and category
    elseif ($nove_hodnotenie['value'] === 'hlavna-cena') {
        $kategoria = get_field('nove_hodnotenie_kategoria');
        ?>
        <div class="absolute bottom-0 left-0 w-full bg-black text-white p-3 z-10">
            <!-- First line: icon, label name, and year -->
            <div class="flex justify-between items-center mb-1">
                <div class="flex items-center">
                    <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg" class="mr-2">
                        <path d="M15.2 1.3H12.8V0.5H3.2V1.3H0.8C0.32 1.3 0 1.62 0 2.1V4.02C0 5.86 1.36 7.38 3.2 7.62V7.7C3.2 10.02 4.8 11.94 6.96 12.42L6.4 14.1H4.56C4.24 14.1 3.92 14.34 3.84 14.66L3.2 16.5H12.8L12.16 14.66C12.08 14.34 11.76 14.1 11.44 14.1H9.6L9.04 12.42C11.2 11.94 12.8 10.02 12.8 7.7V7.62C14.64 7.38 16 5.86 16 4.02V2.1C16 1.62 15.68 1.3 15.2 1.3ZM3.2 6.02C2.32 5.78 1.6 4.98 1.6 4.02V2.9H3.2V6.02ZM9.6 8.5L8 7.62L6.4 8.5L6.8 6.9L5.6 5.3H7.28L8 3.7L8.72 5.3H10.4L9.2 6.9L9.6 8.5ZM14.4 4.02C14.4 4.98 13.68 5.86 12.8 6.02V2.9H14.4V4.02Z" fill="white"/>
                    </svg>
                    <strong><?php echo esc_html($nove_hodnotenie['label']); ?></strong>
                </div>
                <strong><?php echo date('Y'); ?></strong>
            </div>
            <!-- Second line: category -->
            <?php if ($kategoria): ?>
            <ul>
                <li class="tag-list__item">
                    <div class="tag-list__link"><?php
                      echo esc_html($kategoria->name); ?></div>
                </li>
            </ul>
            <?php endif; ?>
        </div>
        <?php
    }
    
    // Here will be added other variations in the future
    // Example: elseif ($nove_hodnotenie['value'] === 'specialna-cena') { ... }
}
?>
